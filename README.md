# 音频转文字网页应用

基于 Vue 3 + Vite + Ant Design Vue 的音频转文字网页应用。

## 功能特性

### 🔐 用户认证
- 手机号 + 短信验证码登录
- 微信第三方登录
- 用户注册（需要激活码）
- JWT Token 认证
- 路由守卫保护

### 🎵 音频转文字
- 支持多种音频格式：MP3、WAV、M4A、AAC、FLAC
- 拖拽上传或点击选择文件
- 实时显示上传和转换进度
- 文件大小限制：100MB
- 转换结果展示和编辑

### 📋 结果管理
- 复制转换结果到剪贴板
- 下载转换结果为 TXT 文件
- 历史记录管理
- 转换统计信息（耗时、文件大小等）

### 📱 响应式设计
- 移动端适配
- 桌面端优化
- 使用 Ant Design Vue 栅格系统

## 技术栈

- **前端框架**: Vue 3 (Composition API + `<script setup>`)
- **构建工具**: Vite
- **UI 组件库**: Ant Design Vue 4.x
- **路由管理**: Vue Router 4.x
- **状态管理**: Pinia
- **HTTP 请求**: Axios
- **样式**: CSS3 + 响应式设计

## 本地开发

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```
