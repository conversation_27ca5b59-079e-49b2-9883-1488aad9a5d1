/**
 * 阿里云 OSS 上传服务
 */

import { http } from '@/utils/request'
import type {
  BaseResponse,
  UploadUrlData,
  TranscriptionTaskRequest,
  TranscriptionTaskData,
  UploadProgress,
} from '@/types/api'

/**
 * OSS 上传服务类
 */
export class OSSService {
  /**
   * 获取文件上传预签名 URL
   * @param filename 文件名
   * @returns 预签名 URL 信息
   */
  static async getUploadUrl(filename: string): Promise<UploadUrlData> {
    const response = await http.get<BaseResponse<UploadUrlData>>('/api/getUploadUrl', {
      params: { filename },
    })

    if (response.data.code !== 200) {
      throw new Error(response.data.message || '获取上传 URL 失败')
    }

    return response.data.data!
  }

  /**
   * 上传文件到 OSS
   * @param file 要上传的文件
   * @param uploadUrl 预签名上传 URL
   * @param onProgress 上传进度回调
   * @returns Promise<void>
   */
  static async uploadFile(
    file: File,
    uploadUrl: string,
    onProgress?: (progress: UploadProgress) => void,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      // 监听上传进度
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
            }
            onProgress(progress)
          }
        })
      }

      // 监听上传完成
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve()
        } else {
          reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`))
        }
      })

      // 监听上传错误
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误，上传失败'))
      })

      // 监听上传中断
      xhr.addEventListener('abort', () => {
        reject(new Error('上传被中断'))
      })

      // 发起 PUT 请求上传文件
      xhr.open('PUT', uploadUrl)
      xhr.setRequestHeader('Content-Type', file.type)
      xhr.send(file)
    })
  }

  /**
   * 创建转写任务
   * @param request 转写任务请求参数
   * @returns 转写任务信息
   */
  static async createTranscriptionTask(
    taskRequest: TranscriptionTaskRequest,
  ): Promise<TranscriptionTaskData> {
    const response = await http.post<BaseResponse<TranscriptionTaskData>>(
      '/api/createTask',
      taskRequest,
    )

    if (response.data.code !== 200) {
      throw new Error(response.data.message || '创建转写任务失败')
    }

    return response.data.data!
  }
}
