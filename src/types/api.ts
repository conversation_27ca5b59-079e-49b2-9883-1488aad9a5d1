/**
 * API 相关类型定义
 */

/**
 * 后台通用响应格式
 */
export interface BaseResponse<T> {
  code: number
  message: string
  data?: T
}

/**
 * 获取上传 URL 响应数据
 */
export interface UploadUrlData {
  /** 预签名上传 URL */
  url: string
  /** OSS 对象名称 */
  objectName: string
  /** 过期时间戳 */
  expiration: number
}

/**
 * 转写任务请求
 */
export interface TranscriptionTaskRequest {
  /** 文件名 */
  fileName: string
  /** 存储文件名，预签名请求中返回的 objectName */
  objectName: string
}

/**
 * 转写任务响应数据
 */
export interface TranscriptionTaskData {
  /** 任务 ID */
  taskId: string
  /** 任务状态 */
  status: string
  /** 创建时间 */
  createTime: string
}

/**
 * 文件上传状态
 */
export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  FAILED = 'failed',
}

/**
 * 上传进度信息
 */
export interface UploadProgress {
  /** 已上传字节数 */
  loaded: number
  /** 总字节数 */
  total: number
  /** 上传百分比 */
  percentage: number
}
