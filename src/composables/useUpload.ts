/**
 * 文件上传组合式函数
 */

import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { OSSService } from '@/services/ossService'
import { validateFileType, validateFileSize } from '@/utils/validation'
import { SUPPORTED_AUDIO_FORMATS, MAX_FILE_SIZE } from '@/utils/constants'
import type { UploadStatus, UploadProgress, TranscriptionTaskData } from '@/types/api'

/**
 * 上传状态接口
 */
interface UploadState {
  status: UploadStatus
  progress: UploadProgress
  error: string | null
  taskData: TranscriptionTaskData | null
}

/**
 * 文件上传 Hook
 */
export function useUpload() {
  // 上传状态
  const uploadState = ref<UploadState>({
    status: 'pending' as UploadStatus,
    progress: { loaded: 0, total: 0, percentage: 0 },
    error: null,
    taskData: null,
  })

  // 当前上传的文件
  const currentFile = ref<File | null>(null)

  // 计算属性
  const isUploading = computed(() => uploadState.value.status === 'uploading')
  const isSuccess = computed(() => uploadState.value.status === 'success')
  const isFailed = computed(() => uploadState.value.status === 'failed')
  const hasFile = computed(() => currentFile.value !== null)

  /**
   * 重置上传状态
   */
  const resetUpload = () => {
    uploadState.value = {
      status: 'pending' as UploadStatus,
      progress: { loaded: 0, total: 0, percentage: 0 },
      error: null,
      taskData: null,
    }
    currentFile.value = null
  }

  /**
   * 验证文件
   * @param file 要验证的文件
   * @returns 验证是否通过
   */
  const validateFile = (file: File): boolean => {
    // 验证文件类型
    if (!validateFileType(file, SUPPORTED_AUDIO_FORMATS)) {
      message.error('不支持的文件格式，请上传 MP3、WAV、M4A、AAC 或 FLAC 格式的音频文件')
      return false
    }

    // 验证文件大小
    if (!validateFileSize(file, MAX_FILE_SIZE)) {
      message.error('文件大小不能超过 100MB')
      return false
    }

    return true
  }

  /**
   * 选择文件
   * @param file 选择的文件
   */
  const selectFile = (file: File): boolean => {
    if (!validateFile(file)) {
      return false
    }

    resetUpload()
    currentFile.value = file
    message.success(`文件 ${file.name} 已选择`)
    return true
  }

  /**
   * 上传进度回调
   * @param progress 上传进度
   */
  const onUploadProgress = (progress: UploadProgress) => {
    uploadState.value.progress = progress
  }

  /**
   * 开始上传并创建转写任务
   */
  const startUpload = async (): Promise<TranscriptionTaskData | null> => {
    if (!currentFile.value) {
      message.error('请先选择文件')
      return null
    }

    try {
      uploadState.value.status = 'uploading' as UploadStatus
      uploadState.value.error = null

      message.info('正在获取上传地址...')

      // 1. 获取预签名上传 URL
      const uploadUrlData = await OSSService.getUploadUrl(currentFile.value.name)

      message.info('开始上传文件...')

      // 2. 上传文件到 OSS
      await OSSService.uploadFile(currentFile.value, uploadUrlData.url, onUploadProgress)

      message.info('文件上传成功，正在创建转写任务...')

      // 3. 创建转写任务
      const taskData = await OSSService.createTranscriptionTask({
        fileName: currentFile.value.name,
        objectName: uploadUrlData.objectName,
      })

      uploadState.value.status = 'success' as UploadStatus
      uploadState.value.taskData = taskData

      message.success('转写任务创建成功！')
      return taskData
    } catch (error) {
      uploadState.value.status = 'failed' as UploadStatus
      uploadState.value.error = error instanceof Error ? error.message : '上传失败'

      message.error(uploadState.value.error)
      return null
    }
  }

  return {
    // 状态
    uploadState: computed(() => uploadState.value),
    currentFile: computed(() => currentFile.value),

    // 计算属性
    isUploading,
    isSuccess,
    isFailed,
    hasFile,

    // 方法
    selectFile,
    startUpload,
    resetUpload,
  }
}
