<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>注册账户</h1>
        <p>创建您的音频转文字账户</p>
      </div>

      <a-form
        :model="formData"
        :rules="rules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
      >
        <a-form-item label="手机号" name="phone">
          <a-input
            v-model:value="formData.phone"
            placeholder="请输入手机号"
            size="large"
            :maxlength="11"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="验证码" name="smsCode">
          <div class="sms-code-container">
            <a-input
              v-model:value="formData.smsCode"
              placeholder="请输入验证码"
              size="large"
              :maxlength="6"
              class="sms-code-input"
            >
              <template #prefix>
                <SafetyOutlined />
              </template>
            </a-input>
            <a-button
              :disabled="!canSendSms || smsLoading"
              :loading="smsLoading"
              @click="sendSmsCode"
              size="large"
              class="sms-code-button"
            >
              {{ smsButtonText }}
            </a-button>
          </div>
        </a-form-item>

        <a-form-item label="激活码" name="activationCode">
          <a-input v-model:value="formData.activationCode" placeholder="请输入激活码" size="large">
            <template #prefix>
              <KeyOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="authStore.loading"
            size="large"
            block
          >
            注册
          </a-button>
        </a-form-item>
      </a-form>

      <div class="register-footer">
        <span>已有账户？</span>
        <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { PhoneOutlined, SafetyOutlined, KeyOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { validatePhone, validateSmsCode, validateActivationCode } from '@/utils/validation'
import { SMS_COOLDOWN_TIME } from '@/utils/constants'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const formData = reactive({
  phone: '',
  smsCode: '',
  activationCode: '',
})

// 短信相关状态
const smsLoading = ref(false)
const smsCountdown = ref(0)
const countdownTimer = ref<number | null>(null)

// 表单验证规则
const rules = {
  phone: [
    { required: true, message: '请输入手机号' },
    {
      validator: (_: unknown, value: string) => {
        if (!value) return Promise.resolve()
        if (!validatePhone(value)) {
          return Promise.reject(new Error('请输入正确的手机号格式'))
        }
        return Promise.resolve()
      },
    },
  ],
  smsCode: [
    { required: true, message: '请输入验证码' },
    {
      validator: (_: unknown, value: string) => {
        if (!value) return Promise.resolve()
        if (!validateSmsCode(value)) {
          return Promise.reject(new Error('请输入6位数字验证码'))
        }
        return Promise.resolve()
      },
    },
  ],
  activationCode: [
    { required: true, message: '请输入激活码' },
    {
      validator: (_: unknown, value: string) => {
        if (!value) return Promise.resolve()
        if (!validateActivationCode(value)) {
          return Promise.reject(new Error('请输入有效的激活码'))
        }
        return Promise.resolve()
      },
    },
  ],
}

// 计算属性
const canSendSms = computed(() => {
  return validatePhone(formData.phone) && smsCountdown.value === 0
})

const smsButtonText = computed(() => {
  return smsCountdown.value > 0 ? `${smsCountdown.value}秒后重新发送` : '发送验证码'
})

// 发送短信验证码
const sendSmsCode = async () => {
  if (!validatePhone(formData.phone)) {
    message.error('请输入正确的手机号')
    return
  }

  smsLoading.value = true
  try {
    await authStore.sendSmsCode(formData.phone)
    message.success('验证码已发送')

    // 开始倒计时
    smsCountdown.value = SMS_COOLDOWN_TIME
    countdownTimer.value = window.setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(countdownTimer.value!)
        countdownTimer.value = null
      }
    }, 1000)
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    smsLoading.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  try {
    await authStore.register(formData.phone, formData.smsCode, formData.activationCode)
    message.success('注册成功')
    router.push('/home')
  } catch (error) {
    console.error('注册失败:', error)
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 40px 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.register-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.register-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.register-form {
  margin-bottom: 24px;
}

.sms-code-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sms-code-input {
  flex: 1;
}

.sms-code-button {
  min-width: 120px;
  white-space: nowrap;
}

.register-footer {
  text-align: center;
  margin-top: 24px;
  color: #6b7280;
}

.register-footer a {
  color: #667eea;
  text-decoration: none;
  margin-left: 8px;
}

.register-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .register-card {
    margin: 0 16px;
    padding: 32px 24px;
  }
}
</style>
