<template>
  <div class="home-container">
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">
            <img src="@/assets/logo.svg" alt="SVG Icon" />
          </div>
          <h1 class="logo-text">AI会议助理</h1>
        </div>
        <div class="header-actions">
          <a-button
            type="text"
            class="header-btn"
            @click="checkAuthAndExecute(() => message.info('语音转文字功能'))"
          >
            <div class="btn-content">
              <img src="@/assets/asr_mic.svg" alt="SVG Icon" class="btn-icon" />
              <span class="btn-text">语音</span>
              <img src="@/assets/asr_to.svg" alt="SVG Icon" class="btn-icon" />
              <img src="@/assets/asr_text.svg" alt="SVG Icon" class="btn-icon" />
              <span class="btn-text">文字</span>
            </div>
          </a-button>
          <a-button
            type="text"
            class="header-btn"
            @click="checkAuthAndExecute(() => message.info('文字转语音功能'))"
          >
            <div class="btn-content">
              <img src="@/assets/asr_text.svg" alt="SVG Icon" class="btn-icon" />
              <span class="btn-text">文字</span>
              <img src="@/assets/asr_to.svg" alt="SVG Icon" class="btn-icon" />
              <img src="@/assets/asr_mic.svg" alt="SVG Icon" class="btn-icon" />
              <span class="btn-text">语音</span>
            </div>
          </a-button>
          <a-button
            type="text"
            class="header-btn"
            @click="checkAuthAndExecute(() => message.info('翻译功能'))"
          >
            <div class="btn-content">
              <img src="@/assets/translate.svg" alt="SVG Icon" class="btn-icon" />
              <span class="btn-text">翻译</span>
            </div>
          </a-button>
        </div>
        <div class="account-actions">
          <!-- 已登录状态：显示用户头像 -->
          <div v-if="authStore.isAuthenticated" class="avatar-container">
            <a-dropdown placement="bottomRight">
              <div class="avatar">
                <img
                  :src="authStore.user?.avatar || '/src/assets/avatar.svg'"
                  :alt="authStore.user?.nickname || '用户头像'"
                  class="avatar-img"
                />
              </div>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    <span>个人信息</span>
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout">
                    <span>退出登录</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <!-- 未登录状态：显示登录按钮 -->
          <div v-else class="auth-buttons">
            <a-button
              type="primary"
              class="auth-btn login-register-btn"
              @click="showLoginModal = true"
            >
              登录/注册
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-wrapper">
        <!-- 左侧音频转换区域 -->
        <div class="upload-card">
          <a-card class="upload-section-card">
            <!-- 1. 麦克风图标 -->
            <div class="microphone-section">
              <div class="microphone-icon">
                <img src="@/assets/asr_mic.svg" alt="麦克风图标" class="microphone-icon-img" />
              </div>
            </div>

            <!-- 2. 文字描述 -->
            <div class="description-section">
              <p class="description-text">点击/拖拽添加音视频，一键转文字生成纪要</p>
            </div>

            <!-- 3. 文件上传区域 -->
            <div class="upload-section">
              <a-upload-dragger
                :before-upload="handleBeforeUpload"
                :show-upload-list="true"
                :multiple="false"
                :max-count="1"
                :disabled="isUploading"
                accept=".mp3,.wav,.m4a,.aac,.flac"
                class="upload-dragger"
              >
                <div class="upload-content">
                  <div class="upload-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor" width="48" height="48">
                      <path
                        d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                      />
                    </svg>
                  </div>
                  <p class="upload-text">
                    {{ isUploading ? '正在上传中...' : '点击或拖拽文件到此区域上传' }}
                  </p>
                </div>
              </a-upload-dragger>
            </div>

            <!-- 上传进度条 -->
            <div v-if="isUploading" class="upload-progress-section">
              <a-progress
                :percent="uploadState.progress.percentage"
                :status="isFailed ? 'exception' : 'active'"
                :show-info="true"
              />
              <p class="progress-text">
                {{ uploadState.progress.loaded }} / {{ uploadState.progress.total }} 字节
              </p>
            </div>

            <!-- 4. 开始转换按钮 -->
            <div class="action-section">
              <a-button
                type="primary"
                size="large"
                class="convert-btn-large"
                :disabled="!hasFile"
                @click="checkAuthAndExecute(startConversion)"
              >
                开始转换
              </a-button>
            </div>
          </a-card>
        </div>

        <!-- 右侧会议记录区域 -->
        <div class="transcription-card">
          <a-card class="transcription-section-card">
            <!-- 空状态 -->
            <div v-if="currentView === 'empty'" class="empty-state">
              <p class="empty-text">请选择音频并点击开始转换，一键转文字生成纪要。</p>
            </div>

            <!-- 列表状态 -->
            <div v-else-if="currentView === 'list'" class="list-state">
              <div class="list-header">
                <h3 class="list-title">近期语音转文字</h3>
                <div class="search-container">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索历史记录"
                    class="search-input"
                  >
                    <template #suffix>
                      <a-button
                        type="primary"
                        class="search-btn"
                        @click="checkAuthAndExecute(() => message.info('搜索功能'))"
                      >
                        查询
                      </a-button>
                    </template>
                  </a-input>
                </div>
              </div>
              <div class="records-list">
                <div
                  v-for="record in filteredRecords"
                  :key="record.id"
                  class="record-item"
                  @click="selectRecord(record)"
                >
                  <span class="record-filename">{{ record.fileName }}</span>
                  <div class="record-actions">
                    <span class="record-time">{{ record.transcriptionTime }}</span>
                    <a-button
                      type="text"
                      danger
                      class="delete-btn"
                      @click.stop="deleteRecord(record.id)"
                    >
                      删除
                    </a-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详情状态 -->
            <div v-else-if="currentView === 'detail' && selectedRecord" class="detail-state">
              <div class="detail-header">
                <a-button type="text" class="back-btn" @click="backToList">← 返回</a-button>
              </div>
              <div class="detail-content">
                <!-- 没有AI总结时的单列布局 -->
                <div v-if="!selectedRecord.aiSummary" class="single-column">
                  <h3 class="detail-title">{{ selectedRecord.fileName }}</h3>
                  <div class="content-card">
                    <p class="transcription-content">{{ selectedRecord.content }}</p>
                  </div>
                  <div class="detail-actions">
                    <a-button
                      v-if="!selectedRecord.aiSummary"
                      class="action-btn ai-summary-btn"
                      @click="checkAuthAndExecute(generateAISummary)"
                    >
                      AI纪要整理
                    </a-button>
                    <a-button
                      class="action-btn save-as-btn"
                      @click="checkAuthAndExecute(saveAs)"
                    >
                      另存为
                    </a-button>
                  </div>
                </div>
                <!-- 有AI总结时的双列布局 -->
                <div v-else class="double-column-container">
                  <div class="double-column">
                    <div class="left-column">
                      <h3 class="detail-title">{{ selectedRecord.fileName }}</h3>
                      <div class="content-card">
                        <p class="transcription-content">{{ selectedRecord.content }}</p>
                      </div>
                    </div>
                    <div class="right-column">
                      <h3 class="detail-title">AI整理纪要</h3>
                      <div class="summary-card">
                        <p class="summary-content">{{ selectedRecord.aiSummary }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="detail-actions">
                    <a-button class="action-btn save-as-btn" @click="saveAs">另存为</a-button>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 备案信息栏 -->
    <div class="footer">
      <div class="footer-content">
        <p class="footer-text">
          © 2024 AI会议助理 | 京ICP备12345678号-1 | 京公网安备11010802012345号
        </p>
      </div>
    </div>

    <!-- 登录弹窗 -->
    <LoginModal
      v-model="showLoginModal"
      @success="handleLoginSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useUpload } from '@/composables/useUpload'
import { useAuthStore } from '@/stores/auth'
import LoginModal from '@/components/LoginModal.vue'

// 使用上传组合式函数
const {
  uploadState,
  currentFile,
  isUploading,
  isSuccess,
  isFailed,
  hasFile,
  selectFile,
  startUpload,
  resetUpload,
} = useUpload()

// 用户认证状态管理
const authStore = useAuthStore()

// 登录弹窗状态
const showLoginModal = ref(false)

// 转写记录接口定义
interface TranscriptionRecord {
  id: string
  fileName: string
  transcriptionTime: string
  content: string
  aiSummary?: string
}

// 转写记录相关状态
const currentView = ref<'empty' | 'list' | 'detail'>('empty') // 当前视图状态
const selectedRecord = ref<TranscriptionRecord | null>(null) // 选中的转写记录
const searchKeyword = ref('') // 搜索关键词

// 转写记录数据（初始为空，可以通过按钮添加示例数据）
const transcriptionRecords = ref<TranscriptionRecord[]>([])

// 示例数据（用于演示）
const sampleRecords: TranscriptionRecord[] = [
  {
    id: '1',
    fileName: '会议记录1.mp3',
    transcriptionTime: '2024-01-15',
    content:
      '教师不仅是知识的传播者，更是学生心灵的引路人。在这个充满挑战与机遇的时代，我们肩负着培养下一代的重要使命。每一堂课，每一次互动，都可能在学生心中播下希望的种子。让我们以耐心和爱心，点亮每一个学生的梦想，用智慧和热情，为他们的未来奠定坚实的基础。',
    aiSummary:
      '本次会议主要讨论了教育工作的重要性和教师的使命。强调了教师不仅要传授知识，更要关注学生的心灵成长，用爱心和耐心引导学生，为他们的未来发展奠定基础。\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\naaaaaaaaaa',
  },
  {
    id: '2',
    fileName: '项目讨论.mp3',
    transcriptionTime: '2024-01-14',
    content:
      '今天我们讨论了新项目的进展情况，包括技术选型、人员分配和时间安排。大家对Vue3和TypeScript的组合表示认可，认为这能提高开发效率和代码质量。',
  },
  {
    id: '3',
    fileName: '客户沟通.mp3',
    transcriptionTime: '2024-01-13',
    content:
      '与客户就产品需求进行了深入沟通，明确了核心功能点和优先级。客户对我们的技术方案表示满意，希望能在下个月完成第一版原型。',
    aiSummary: '客户沟通会议确定了产品核心需求和开发优先级，客户对技术方案满意，要求下月交付原型。',
  },
]

// 文件列表数据（保留原有的，用于其他功能）
const fileList = ref([
  { name: '会议记录1.mp3', status: '转录中' },
  { name: '会议记录2.mp3', status: '暂停' },
])

// 计算属性：过滤后的转写记录
const filteredRecords = computed(() => {
  if (!searchKeyword.value) {
    return transcriptionRecords.value
  }
  return transcriptionRecords.value.filter((record) =>
    record.fileName.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

// 方法：选择转写记录
const selectRecord = (record: TranscriptionRecord) => {
  selectedRecord.value = record
  currentView.value = 'detail'
}

// 方法：返回列表
const backToList = () => {
  currentView.value = 'list'
  selectedRecord.value = null
}

// 方法：删除转写记录
const deleteRecord = (recordId: string) => {
  const index = transcriptionRecords.value.findIndex((r) => r.id === recordId)
  if (index > -1) {
    transcriptionRecords.value.splice(index, 1)
    message.success('删除成功')
    if (transcriptionRecords.value.length === 0) {
      currentView.value = 'empty'
    }
  }
}

// 方法：AI纪要整理
const generateAISummary = () => {
  if (selectedRecord.value) {
    // 模拟AI生成摘要
    selectedRecord.value.aiSummary = '这是AI生成的会议纪要摘要，包含了主要讨论点和结论。'
    message.success('AI纪要整理完成')
  }
}

// 方法：另存为
const saveAs = () => {
  message.success('文件已保存')
}

// 用户认证相关方法
const handleLogout = () => {
  authStore.logout()
  message.success('已退出登录')
}

const handleLoginSuccess = () => {
  // 登录成功后可以执行一些初始化操作
  message.success('登录成功')
}

// 检查登录状态的方法
const checkAuthAndExecute = (callback: () => void) => {
  if (!authStore.isAuthenticated) {
    showLoginModal.value = true
    return
  }
  callback()
}

// 处理文件上传前的验证
const handleBeforeUpload = (file: File) => {
  // 使用 useUpload 的 selectFile 方法进行验证和选择
  selectFile(file)
  return false // 阻止默认上传行为
}

// 开始转换（上传并创建转写任务）
const startConversion = async () => {
  if (!currentFile.value) {
    message.error('请先选择文件')
    return
  }

  try {
    // 使用新的上传流程
    const taskData = await startUpload()

    if (taskData) {
      // 生成新的转写记录
      const newRecord: TranscriptionRecord = {
        id: taskData.taskId,
        fileName: currentFile.value!.name,
        transcriptionTime: new Date().toISOString().split('T')[0],
        content: '正在转写中，请稍候...',
      }

      // 添加到转写记录列表
      transcriptionRecords.value.unshift(newRecord)

      // 如果是第一条记录，添加示例数据以便演示
      if (transcriptionRecords.value.length === 1) {
        transcriptionRecords.value.push(...sampleRecords)
      }

      // 切换到列表视图
      currentView.value = 'list'

      // 添加到文件列表（保留原有功能）
      fileList.value.unshift({
        name: currentFile.value!.name,
        status: '转写中',
      })

      // 重置上传状态
      resetUpload()
    }
  } catch (error) {
    console.error('转换失败:', error)
  }
}

// 页面加载时获取用户信息
onMounted(async () => {
  if (authStore.token) {
    try {
      await authStore.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  filter: none !important;
  opacity: 1 !important;
  background: none !important;
}

.btn-icon {
  width: 18px;
  height: 18px;
  filter: none !important;
  opacity: 1 !important;
  background: none !important;
  flex-shrink: 0;
}

.btn-text {
  color: inherit;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}
.home-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  width: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 0 0 auto;
}

.logo-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 1;
}

.account-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.avatar-container {
  display: flex;
  align-items: center;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.avatar-img {
  width: 50%;
  height: 50%;
  object-fit: cover;
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-register-btn {
  width: 132px;
  height: 50px;
  background: #1b7846;
  border-radius: 25px;
  border-color: #1b7846;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-register-btn:hover {
  background: #249659;
  border-color: #249659;
}

.header-btn {
  height: 50px;
  padding: 8px 16px;
  border-radius: 25px;
  border: none;
  background: rgba(0, 0, 0, 0.03);
  color: #1f2937;
  transition: all 0.3s ease;
}

.header-btn:hover {
  background: rgba(27, 120, 70, 0.1);
  color: #1f2937;
}

.header-btn.active {
  background: rgba(27, 120, 70, 0.1);
  color: #1f2937;
}

.header-btn .btn-text {
  color: #1f2937 !important;
}

.trial-btn {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
}

.main-content {
  flex: 1;
  width: 100%;
  padding: 0;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 64px - 50px);
  gap: 24px;
  padding: 24px;
  justify-content: center;
  max-width: 2400px;
  margin: 0 auto;
}

/* 左侧音频转换区域 - 响应式布局 1:2 比例，最大800px */
.upload-card {
  flex: 1;
  min-width: 300px;
  max-width: 800px;
  display: flex;
  flex-direction: column;
}

.upload-section-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  overflow: hidden;
}

.upload-section-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  text-align: center;
  min-height: 0;
}

/* 1. 麦克风图标区域 */
.microphone-section {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.microphone-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.03);
}

.microphone-icon-img {
  width: 40%;
  height: 40%;
}

/* 2. 文字描述区域 */
.description-section {
  margin-bottom: 32px;
  text-align: center;
}

.main-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  text-align: center;
}

.description-text {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.6);
  margin: 0 0 30px 0;
  line-height: 1;
  text-align: center;
}

.support-formats {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
  text-align: center;
}

/* 3. 文件上传区域 */
.upload-section {
  height: 260px;
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
}

/* 上传进度区域 */
.upload-progress-section {
  margin: 24px 0;
  padding: 0 24px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 0;
}

.upload-dragger {
  border: 2px dashed #d1d5db !important;
  border-radius: 12px !important;
  background: #f9fafb !important;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: #667eea !important;
  background: #f0f4ff !important;
}

/* 自定义上传列表项高度 */
.upload-dragger :deep(.ant-upload-list-item) {
  height: 40px !important;
  line-height: 40px !important;
  padding: 0 12px !important;
}

.upload-dragger :deep(.ant-upload-list-item-info) {
  height: 40px !important;
  line-height: 40px !important;
}

.upload-dragger :deep(.ant-upload-list-item-name) {
  line-height: 40px !important;
}

.upload-dragger :deep(.ant-upload-list-item-actions) {
  height: 40px !important;
  line-height: 40px !important;
}

.upload-content {
  padding: 24px;
}

.upload-icon {
  color: #9ca3af;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #374151;
  margin: 0;
  font-weight: 500;
}

/* 4. 开始转换按钮区域 */
.action-section {
  margin-top: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.convert-btn-large {
  width: 260px;
  height: 80px;
  background: #1b7846;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.convert-btn-large:hover:not(:disabled) {
  background: #249659;
}

.convert-btn-large:disabled {
  background: #e5e7eb;
  color: #9ca3af;
}

/* 右侧转写记录区域 - 响应式布局 1:2 比例，最大1600px */
.transcription-card {
  flex: 2;
  max-width: 1600px;
}

.transcription-section-card {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  border-radius: 20px;
}

.transcription-section-card :deep(.ant-card-body) {
  flex: 1 !important;
  padding: 24px;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin: 0;
  text-align: center;
}

/* 列表状态样式 */
.list-state {
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
}

.list-header {
  width: 70%;
  margin-bottom: 24px;
}

.list-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0 0 16px 0;
  text-align: center;
}

.search-container {
  width: 100%;
}

.search-input {
  height: 60px;
  border-radius: 20px;
  border: 1px solid #d9d9d9;
}

.search-input .ant-input {
  height: 58px;
  border: none;
  border-radius: 20px;
  padding: 0 20px;
}

.search-btn {
  background: #52c41a;
  border-color: #52c41a;
  border-radius: 15px;
  height: 40px;
  margin-right: 10px;
}

.records-list {
  width: 70%;
  flex: 1;
  overflow-y: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  border-radius: 20px;
  border: 1px solid #e8e8e8;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.record-item:hover {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.record-filename {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.record-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-time {
  font-size: 14px;
  color: #666;
}

.delete-btn {
  color: #ff4d4f;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
}

/* 详情状态样式 */
.detail-state {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  margin-bottom: 20px;
}

.back-btn {
  color: #1890ff;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
}

.detail-content {
  flex: 1;
  overflow: hidden;
}

.single-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.double-column-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.double-column {
  display: flex;
  gap: 24px;
  flex: 1;
  min-height: 0;
}

.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0 0 16px 0;
}

.content-card {
  flex: 1;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  overflow-y: auto;
  min-height: 0;
}

.summary-card {
  flex: 1;
  background: #f6ffed;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  overflow-y: auto;
  min-height: 0;
}

.transcription-content,
.summary-content {
  font-size: 14px;
  color: #000;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
}

.detail-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  width: 100%;
}

.action-btn {
  border-radius: 20px;
  width: 150px;
  height: 50px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* AI纪要整理按钮样式 */
.action-btn.ai-summary-btn {
  background: #e5e5e5;
  border-color: #e5e5e5;
  color: #333;
}

.action-btn.ai-summary-btn:hover {
  background: #d0d0d0;
  border-color: #d0d0d0;
  color: #333;
}

/* 另存为按钮样式 */
.action-btn.save-as-btn {
  background: #1b7847;
  border-color: #1b7847;
  color: white;
}

.action-btn.save-as-btn:hover {
  background: #249659;
  border-color: #249659;
  color: white;
}

/* 左右布局容器 */
.transcription-layout {
  display: flex;
  height: 100%;
  gap: 16px;
  flex: 1;
  min-height: 0;
  padding: 24px;
}

/* 左侧记录列表区域 */
.records-list-section {
  width: 300px;
  min-width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  height: 880px;
}

/* 右侧内容区域 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
}

.file-list {
  background: #fafafa;
  border-radius: 8px;
  padding: 12px;
  height: 100%;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
}

.file-item {
  padding: 8px 12px;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.2s;
  border-radius: 4px;
  margin-bottom: 4px;
}

.file-item:hover {
  background: #f0f0f0;
}

.file-item.active {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.file-status {
  font-size: 11px;
  color: #52c41a;
}

.meeting-header {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.meeting-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.meeting-content {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  max-height: 800px;
}

.meeting-text {
  line-height: 1.8;
  color: #374151;
  font-size: 14px;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  flex: 1;
  overflow-y: auto;
}

.meeting-text p {
  margin: 0 0 16px 0;
  white-space: pre-line;
}

.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-area {
  flex: 1;
  margin-bottom: 16px;
}

.upload-dragger {
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s;
  min-height: 150px;
  height: 100%;
}

.upload-dragger:hover {
  border-color: #52c41a;
}

.upload-content {
  padding: 20px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-icon {
  color: #52c41a;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.upload-text {
  font-size: 14px;
  color: #1f2937;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.upload-hint {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.convert-btn {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
  height: 40px;
  padding: 0 32px;
  border-radius: 6px;
  font-weight: 500;
}

.convert-btn:hover {
  background: #389e0d;
  border-color: #389e0d;
}

.convert-btn:disabled {
  background: #d9d9d9;
  border-color: #d9d9d9;
  color: #999;
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    height: auto;
    padding: 16px 0;
    gap: 16px;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .content-wrapper {
    flex-direction: column;
    height: auto;
    padding: 16px;
    gap: 16px;
  }

  .upload-card,
  .transcription-card {
    flex: none;
    max-width: none;
  }

  .upload-section-card,
  .transcription-section-card {
    height: auto;
  }

  .file-list {
    max-height: 150px;
  }

  .upload-content {
    padding: 16px;
  }

  .meeting-text {
    min-height: 200px;
  }
}

@media (max-width: 576px) {
  .header-actions {
    gap: 4px;
  }

  .header-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .trial-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .convert-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 备案信息栏样式 */
.footer {
  height: 50px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.footer-content {
  max-width: 2400px;
  width: 100%;
  padding: 0 24px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .footer-content {
    padding: 0 16px;
  }

  .footer-text {
    font-size: 11px;
  }
}
</style>
