import { createRouter, createWebHistory } from 'vue-router'
import { TOKEN_KEY, ROUTES } from '@/utils/constants'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: false },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem(TOKEN_KEY)
  const isAuthenticated = !!token

  // 需要认证的路由
  if (to.meta.requiresAuth && !isAuthenticated) {
    next(ROUTES.LOGIN)
    return
  }

  // 只允许游客访问的路由（如登录、注册页）
  if (to.meta.requiresGuest && isAuthenticated) {
    next(ROUTES.HOME)
    return
  }

  next()
})

export default router
