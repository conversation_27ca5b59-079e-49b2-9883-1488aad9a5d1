import { createRouter, createWebHistory } from 'vue-router'
import { TOKEN_KEY, ROUTES } from '@/utils/constants'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: false },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem(TOKEN_KEY)
  const isAuthenticated = !!token

  // 需要认证的路由
  if (to.meta.requiresAuth && !isAuthenticated) {
    // 由于改为弹窗登录，直接跳转到首页，由首页组件处理登录弹窗
    next(ROUTES.HOME)
    return
  }

  next()
})

export default router
