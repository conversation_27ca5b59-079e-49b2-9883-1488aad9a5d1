import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import request from '@/utils/request'
import { TOKEN_KEY } from '@/utils/constants'

export interface User {
  id: string
  phone: string
  nickname?: string
  avatar?: string
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem(TOKEN_KEY))
  const user = ref<User | null>(null)
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)

  // 登录
  const login = async (phone: string, smsCode: string) => {
    loading.value = true
    try {
      const response = await request.post('/auth/login', {
        phone,
        smsCode,
      })

      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem(TOKEN_KEY, response.data.token)

      return response
    } finally {
      loading.value = false
    }
  }

  // 微信登录
  const wechatLogin = async (code: string) => {
    loading.value = true
    try {
      const response = await request.post('/auth/wechat-login', {
        code,
      })

      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem(TOKEN_KEY, response.data.token)

      return response
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (phone: string, smsCode: string, activationCode: string) => {
    loading.value = true
    try {
      const response = await request.post('/auth/register', {
        phone,
        smsCode,
        activationCode,
      })

      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem(TOKEN_KEY, response.data.token)

      return response
    } finally {
      loading.value = false
    }
  }

  // 发送短信验证码
  const sendSmsCode = async (phone: string) => {
    return await request.post('/auth/send-sms', {
      phone,
    })
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem(TOKEN_KEY)
  }

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value) return null

    try {
      const response = await request.get('/auth/user')
      user.value = response.data
      return response.data
    } catch (error) {
      // 如果获取用户信息失败，可能是 token 过期
      logout()
      throw error
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    wechatLogin,
    register,
    sendSmsCode,
    logout,
    getUserInfo,
  }
})
