<template>
  <a-modal
    v-model:open="visible"
    title="登录/注册"
    :footer="null"
    :width="400"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="login-modal-content">
      <!-- 手机号输入 -->
      <div class="form-item">
        <label class="form-label">手机号</label>
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入手机号"
          size="large"
          :maxlength="11"
          class="form-input"
        />
      </div>

      <!-- 验证码输入 -->
      <div class="form-item">
        <label class="form-label">验证码</label>
        <div class="verification-input">
          <a-input
            v-model:value="formData.smsCode"
            placeholder="请输入验证码"
            size="large"
            :maxlength="6"
            class="code-input"
          />
          <a-button
            :disabled="!canSendSms"
            :loading="sendingSms"
            @click="handleSendSms"
            class="send-code-btn"
          >
            {{ smsButtonText }}
          </a-button>
        </div>
      </div>

      <!-- 激活码输入（注册时需要） -->
      <div class="form-item">
        <label class="form-label">
          激活码
          <span class="optional-text">（注册时需要）</span>
        </label>
        <a-input
          v-model:value="formData.activationCode"
          placeholder="请输入激活码（可选）"
          size="large"
          class="form-input"
        />
      </div>

      <!-- 登录/注册按钮 -->
      <div class="form-item">
        <a-button
          type="primary"
          size="large"
          :loading="authStore.loading"
          :disabled="!canSubmit"
          @click="handleSubmit"
          class="submit-btn"
        >
          {{ formData.activationCode ? '注册' : '登录' }}
        </a-button>
      </div>

      <!-- 分割线 -->
      <div class="divider">
        <span class="divider-text">或</span>
      </div>

      <!-- 微信登录 -->
      <div class="form-item">
        <a-button
          size="large"
          @click="handleWechatLogin"
          class="wechat-login-btn"
        >
          <img src="@/assets/wechat-icon.svg" alt="微信" class="wechat-icon" />
          微信登录
        </a-button>
      </div>

      <!-- 提示文本 -->
      <div class="tips">
        <p class="tip-text">
          • 首次使用需要激活码进行注册
        </p>
        <p class="tip-text">
          • 已注册用户直接输入手机号和验证码登录
        </p>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '@/stores/auth'
import { PHONE_REGEX, SMS_COOLDOWN_TIME, WECHAT_CONFIG } from '@/utils/constants'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = ref({
  phone: '',
  smsCode: '',
  activationCode: ''
})

// 短信验证码相关
const sendingSms = ref(false)
const countdown = ref(0)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const canSendSms = computed(() => {
  return PHONE_REGEX.test(formData.value.phone) && countdown.value === 0
})

const canSubmit = computed(() => {
  return PHONE_REGEX.test(formData.value.phone) && 
         formData.value.smsCode.length === 6
})

const smsButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s` : '发送验证码'
})

// 发送短信验证码
const handleSendSms = async () => {
  if (!canSendSms.value) return

  sendingSms.value = true
  try {
    await authStore.sendSmsCode(formData.value.phone)
    message.success('验证码发送成功')
    
    // 开始倒计时
    countdown.value = SMS_COOLDOWN_TIME
    countdownTimer.value = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!)
        countdownTimer.value = null
      }
    }, 1000)
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    sendingSms.value = false
  }
}

// 提交表单（登录或注册）
const handleSubmit = async () => {
  if (!canSubmit.value) return

  try {
    if (formData.value.activationCode) {
      // 注册
      await authStore.register(
        formData.value.phone,
        formData.value.smsCode,
        formData.value.activationCode
      )
      message.success('注册成功')
    } else {
      // 登录
      await authStore.login(formData.value.phone, formData.value.smsCode)
      message.success('登录成功')
    }
    
    emit('success')
    handleCancel()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

// 微信登录
const handleWechatLogin = () => {
  if (!WECHAT_CONFIG.APP_ID) {
    message.error('微信登录配置错误')
    return
  }

  const redirectUri = encodeURIComponent(WECHAT_CONFIG.REDIRECT_URI)
  const wechatUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${WECHAT_CONFIG.APP_ID}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=login#wechat_redirect`

  window.location.href = wechatUrl
}

// 取消弹窗
const handleCancel = () => {
  visible.value = false
  // 重置表单
  formData.value = {
    phone: '',
    smsCode: '',
    activationCode: ''
  }
  // 清理倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
    countdown.value = 0
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.login-modal-content {
  padding: 20px 0;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.optional-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: normal;
}

.form-input {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 14px;
}

.form-input:focus {
  border-color: #1b7846;
  box-shadow: 0 0 0 2px rgba(27, 120, 70, 0.1);
}

.verification-input {
  display: flex;
  gap: 12px;
}

.code-input {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 14px;
}

.send-code-btn {
  width: 100px;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #1b7846;
  color: #1b7846;
  background: white;
  font-size: 14px;
}

.send-code-btn:hover:not(:disabled) {
  background: #f0f9ff;
  border-color: #249659;
  color: #249659;
}

.send-code-btn:disabled {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #9ca3af;
}

.submit-btn {
  width: 100%;
  height: 44px;
  background: #1b7846;
  border-color: #1b7846;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.submit-btn:hover:not(:disabled) {
  background: #249659;
  border-color: #249659;
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.divider-text {
  background: white;
  padding: 0 16px;
  color: #6b7280;
  font-size: 14px;
}

.wechat-login-btn {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #07c160;
  color: #07c160;
  background: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.wechat-login-btn:hover {
  background: #f0f9ff;
  border-color: #07c160;
  color: #07c160;
}

.wechat-icon {
  width: 20px;
  height: 20px;
}

.tips {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.tip-text {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
}

.tip-text:last-child {
  margin-bottom: 0;
}
</style>
