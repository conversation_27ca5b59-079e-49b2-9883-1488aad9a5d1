# ESLint 配置说明

## 当前状态

✅ **ESLint 已成功配置并正常工作**

ESLint 使用 flat config 格式，支持 Vue 3 + TypeScript 项目的代码检查。

## 配置详情

### 使用的配置文件

- `eslint.config.js` - 使用 ESLint 9.x 的 flat config 格式

### 主要特性

1. **Vue 3 支持**：正确解析 `.vue` 文件和 Composition API
2. **TypeScript 支持**：完整的 TypeScript 类型检查
3. **代码格式化集成**：与 Prettier 配置兼容
4. **自动修复**：支持 `--fix` 参数自动修复可修复的问题

### 当前 lint 脚本

```json
"lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix"
```

## 配置文件内容

当前使用的 `eslint.config.js` 配置：

```javascript
import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import * as parserVue from 'vue-eslint-parser'
import configTypeScript from '@vue/eslint-config-typescript'
import configPrettier from '@vue/eslint-config-prettier'

export default [
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },
  {
    name: 'app/files-to-ignore',
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**', '**/node_modules/**'],
  },
  js.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  ...configTypeScript(),
  configPrettier,
  {
    name: 'app/vue-rules',
    files: ['**/*.vue'],
    languageOptions: {
      parser: parserVue,
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'warn',
      'no-console': 'off',
      'vue/multi-word-component-names': 'off',
      'vue/no-reserved-component-names': 'off',
    },
  },
  {
    name: 'app/typescript-rules',
    files: ['**/*.{ts,mts,tsx}'],
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'warn',
      'no-console': 'off',
    },
  },
]
```

## 功能状态

- ✅ **应用运行**：正常运行
- ✅ **TypeScript 检查**：`npm run type-check` 正常工作
- ✅ **构建**：`npm run build` 正常工作
- ✅ **代码格式化**：`npm run format` 正常工作
- ✅ **代码规范检查**：`npm run lint` 正常工作并支持自动修复

## 使用方法

### 检查代码规范

```bash
npm run lint
```

### 检查并自动修复

```bash
npm run lint
```

（已包含 `--fix` 参数）

### 检查特定文件

```bash
npx eslint src/components/MyComponent.vue
```

## 规则说明

### Vue 文件规则

- 允许单词组件名（`vue/multi-word-component-names: off`）
- 允许保留组件名（`vue/no-reserved-component-names: off`）
- 未使用变量显示警告而非错误

### TypeScript 文件规则

- 未使用变量显示警告而非错误
- 允许 console 语句（开发阶段）

### 通用规则

- 集成 Prettier 格式化规则
- 支持最新 ECMAScript 语法
