# API 接口模拟说明

由于这是前端项目，后端 API 需要单独开发。以下是 API 接口的详细说明和模拟数据格式。

## 认证相关接口

### 1. 发送短信验证码

**接口**: `POST /api/auth/send-sms`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "验证码已发送",
  "data": {
    "expires": 300
  }
}
```

### 2. 用户登录

**接口**: `POST /api/auth/login`

**请求参数**:
```json
{
  "phone": "13800138000",
  "smsCode": "123456"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "phone": "13800138000",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg"
    }
  }
}
```

### 3. 用户注册

**接口**: `POST /api/auth/register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "smsCode": "123456",
  "activationCode": "ABCD1234"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "phone": "13800138000",
      "nickname": null,
      "avatar": null
    }
  }
}
```

### 4. 微信登录

**接口**: `POST /api/auth/wechat-login`

**请求参数**:
```json
{
  "code": "wechat_auth_code"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "phone": "13800138000",
      "nickname": "微信用户",
      "avatar": "https://wx.qlogo.cn/..."
    }
  }
}
```

### 5. 获取用户信息

**接口**: `GET /api/auth/user`

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "user_123",
    "phone": "13800138000",
    "nickname": "用户昵称",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

## 音频转换相关接口

### 1. 音频文件上传

**接口**: `POST /api/audio/upload`

**请求**: multipart/form-data
- file: 音频文件

**响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "taskId": "task_123456",
    "fileUrl": "https://example.com/uploads/audio.mp3"
  }
}
```

### 2. 查询转换状态

**接口**: `GET /api/audio/convert/{taskId}`

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "taskId": "task_123456",
    "status": "processing", // idle, uploading, processing, completed, error
    "progress": 65,
    "error": null
  }
}
```

### 3. 获取转换结果

**接口**: `GET /api/audio/result/{taskId}`

**响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "taskId": "task_123456",
    "text": "这是转换后的文字内容...",
    "duration": 120000,
    "fileSize": 5242880
  }
}
```

## 错误响应格式

所有接口在出错时返回统一格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

常见错误码：
- 400: 请求参数错误
- 401: 未授权（token 无效或过期）
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

## 开发建议

1. 可以使用 JSON Server 或 Mock.js 创建模拟 API
2. 或者直接修改前端代码，在开发阶段返回模拟数据
3. 建议先完成前端界面和交互逻辑，再对接真实 API
